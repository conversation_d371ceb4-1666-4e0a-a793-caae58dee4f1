'use client';

import Image from 'next/image';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { SafeHtml } from '@/components/ui/safe-html';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  RefreshCw,
  ExternalLink,
  Info,
  ShoppingCart
} from 'lucide-react';
import { Product } from '@/types/product';
import { formatDate } from '@/lib/utils';
import { useSynchronizeProducts } from '@/lib/hooks';

interface ProductDetailsProps {
  product: Product;
}

export function ProductDetails({ product }: ProductDetailsProps) {
  // Sync mutation for refreshing product details
  const { mutate: synchronizeProducts, isPending: isSynchronizing } = useSynchronizeProducts();

  // Helper function to get image URL
  const getImageUrl = (image: any): string | null => {
    if (typeof image === 'string') {
      return image;
    }
    return image?.urls?.[0] || image?.url || null;
  };

  // Helper function to get SKU image URL
  const getSkuImageUrl = (sku: any): string | null => {
    // Check if any sales attribute has a skuImg
    if (sku.salesAttributes && sku.salesAttributes.length > 0) {
      for (const attr of sku.salesAttributes) {
        if (attr.skuImg?.urls?.[0]) {
          return attr.skuImg.urls[0];
        }
      }
    }
    return null;
  };

  // Handle sync product details
  const handleSyncDetails = () => {
    if (!product.tiktokShopId) return;
    
    synchronizeProducts({
      tiktokShopId: product.tiktokShopId,
      pageSize: 1,
      sortOrder: 'DESC',
      sortField: 'update_time'
    });
  };

  // Format status for display
  const formatStatus = (status: string) => {
    switch (status) {
      case 'PLATFORM_DEACTIVATED':
        return 'Platform Deactivated';
      case 'SELLER_DEACTIVATED':
        return 'Seller Deactivated';
      default:
        return status.charAt(0) + status.slice(1).toLowerCase();
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'AUDITING':
        return 'bg-blue-100 text-blue-800';
      case 'ACTIVATE':
        return 'bg-green-100 text-green-800';
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      case 'FREEZE':
        return 'bg-purple-100 text-purple-800';
      case 'PLATFORM_DEACTIVATED':
      case 'SELLER_DEACTIVATED':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{product.title}</h2>
          <p className="text-muted-foreground">
            Created {formatDate(product.createdAt)} • Last updated {formatDate(product.updatedAt)}
          </p>
          {product.idTT && (
            <p className="text-sm text-muted-foreground">
              TikTok Product ID: {product.idTT}
            </p>
          )}
        </div>
        <div className="flex flex-col gap-2">
          {/* Action buttons */}
          <div className="flex items-center gap-2">
            {product.idTT && (
              <Button variant="outline" asChild>
                <a
                  href={`https://seller-us.tiktok.com/product/list/${product.idTT}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View on TikTok Shop
                </a>
              </Button>
            )}
          </div>
        </div>
      </div>

      <Tabs defaultValue="details">
        <TabsList>
          <TabsTrigger value="details">
            <Info className="mr-2 h-4 w-4" />
            Details
          </TabsTrigger>
          <TabsTrigger value="skus">
            <ShoppingCart className="mr-2 h-4 w-4" />
            SKUs ({product.skus?.length || 0})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="grid gap-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Status</h3>
                  <Badge className={getStatusBadgeColor(product.status)}>
                    {formatStatus(product.status)}
                  </Badge>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Category</h3>
                  <p>{product.categoryChains?.[product.categoryChains.length - 1]?.localName || product.category || '-'}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Brand</h3>
                  <p>{product.brand?.name || product.brandInfo || '-'}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">SKU Count</h3>
                  <p>{product.skus?.length || 0} SKU{(product.skus?.length || 0) !== 1 ? 's' : ''}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Images</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {product.mainImages && product.mainImages.length > 0 ? (
                  product.mainImages.map((image, index) => {
                    const imageUrl = getImageUrl(image);
                    return imageUrl ? (
                      <div key={index} className="relative aspect-square rounded-md overflow-hidden border">
                        <Image
                          src={imageUrl}
                          alt={`Product image ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ) : null;
                  }).filter(Boolean)
                ) : product.productImages && product.productImages.length > 0 ? (
                  product.productImages.map((imageUrl, index) => (
                    <div key={index} className="relative aspect-square rounded-md overflow-hidden border">
                      <Image
                        src={imageUrl}
                        alt={`Product image ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                    </div>
                  ))
                ) : (
                  <p className="text-muted-foreground col-span-full">No images available</p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Size Chart</CardTitle>
            </CardHeader>
            <CardContent>
              {product.sizeChart?.image?.urls?.[0] ? (
                <div className="relative h-64 w-full md:w-1/2 rounded-md overflow-hidden border">
                  <Image
                    src={product.sizeChart.image.urls[0]}
                    alt="Size chart"
                    fill
                    className="object-contain"
                  />
                </div>
              ) : (
                <p className="text-muted-foreground">Size chart information will be displayed here when available.</p>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <SafeHtml
                html={product.description || 'No description provided.'}
                className="prose prose-sm max-w-none [&>*:first-child]:mt-0 [&>*:last-child]:mb-0"
              />
            </CardContent>
          </Card>

          {product.packageDimensions && (
            <Card>
              <CardHeader>
                <CardTitle>Package Information</CardTitle>
              </CardHeader>
              <CardContent className="grid gap-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Dimensions</h3>
                    <p>
                      {product.packageDimensions.length} × {product.packageDimensions.width} × {product.packageDimensions.height} {product.packageDimensions.unit}
                    </p>
                  </div>
                  {product.packageWeight && (
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Weight</h3>
                      <p>
                        {product.packageWeight.value} {product.packageWeight.unit}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="skus" className="mt-4">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Attributes</TableHead>
                    <TableHead>Images</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Inventory</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {product.skus && product.skus.length > 0 ? (
                    product.skus.map((sku, index) => {
                      const skuImageUrl = getSkuImageUrl(sku);
                      return (
                        <TableRow key={index}>
                          <TableCell>
                            {sku.salesAttributes && sku.salesAttributes.length > 0 ? (
                              <div className="flex flex-wrap gap-1">
                                {sku.salesAttributes.map((attr, i) => (
                                  <Badge key={i} variant="outline">
                                    {attr.name}: {attr.valueName}
                                  </Badge>
                                ))}
                              </div>
                            ) : (
                              'No attributes'
                            )}
                          </TableCell>
                          <TableCell>
                            {skuImageUrl ? (
                              <div className="relative w-12 h-12 rounded-md overflow-hidden border">
                                <Image
                                  src={skuImageUrl}
                                  alt={`SKU image`}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                            ) : (
                              <div className="w-12 h-12 rounded-md border border-dashed border-gray-300 flex items-center justify-center">
                                <span className="text-xs text-muted-foreground">No image</span>
                              </div>
                            )}
                          </TableCell>
                        <TableCell>
                          {sku.price ? (
                            <div className="space-y-1">
                              <div>
                                <span className="font-medium">
                                  {sku.price.currency} {sku.price.salePrice?.toFixed(2)}
                                </span>
                              </div>
                              {sku.price.taxExclusivePrice && (
                                <div className="text-xs text-muted-foreground">
                                  Tax Exclusive: {sku.price.currency} {sku.price.taxExclusivePrice.toFixed(2)}
                                </div>
                              )}
                            </div>
                          ) : (
                            'N/A'
                          )}
                        </TableCell>
                        <TableCell>
                          {sku.inventory && sku.inventory.length > 0 ? (
                            <div className="space-y-1">
                              {sku.inventory.map((inv, i) => (
                                <div key={i} className="text-sm">
                                  <span className="font-medium">{inv.quantity} units</span>
                                  {inv.warehouseId && (
                                    <span className="text-muted-foreground ml-1">
                                      (Warehouse: {inv.warehouseId})
                                    </span>
                                  )}
                                </div>
                              ))}
                            </div>
                          ) : (
                            'No inventory'
                          )}
                        </TableCell>
                      </TableRow>
                    );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4">
                        No SKUs available for this product
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
